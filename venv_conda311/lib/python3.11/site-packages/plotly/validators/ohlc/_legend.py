#                   --- THIS FILE IS AUTO-GENERATED ---
# Modifications will be overwitten the next time code generation run.

import _plotly_utils.basevalidators as _bv


class LegendValidator(_bv.SubplotidValidator):
    def __init__(self, plotly_name="legend", parent_name="ohlc", **kwargs):
        super().__init__(
            plotly_name,
            parent_name,
            dflt=kwargs.pop("dflt", "legend"),
            edit_type=kwargs.pop("edit_type", "style"),
            **kwargs,
        )
