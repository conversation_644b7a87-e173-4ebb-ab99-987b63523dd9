#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
恒生指数成分股ROE因子回测工具（简化版）

功能：
1. ROE因子有效性回测
2. 分组回测分析
3. 风险收益评估
4. 可视化结果展示

作者: AI Assistant
创建时间: 2025年1月
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
import os
from typing import Dict, List, Tuple, Optional

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class HSIROEFactorBacktestSimple:
    """恒生指数ROE因子回测器（简化版）"""
    
    def __init__(self, data_file: str = None):
        self.data_file = data_file
        self.df = None
        self.backtest_results = {}
        
        # 如果没有指定文件，自动查找最新的合并数据文件
        if data_file is None:
            self.data_file = self.find_latest_combined_file()
    
    def find_latest_combined_file(self) -> str:
        """查找最新的合并数据文件"""
        cache_dir = "hsi_roe_cache"
        if not os.path.exists(cache_dir):
            raise FileNotFoundError("未找到ROE数据缓存目录")
        
        combined_files = [f for f in os.listdir(cache_dir) if f.startswith('hsi_daily_roe_combined_')]
        
        if not combined_files:
            raise FileNotFoundError("未找到ROE合并数据文件")
        
        combined_files.sort(reverse=True)
        latest_file = os.path.join(cache_dir, combined_files[0])
        
        print(f"📁 使用数据文件: {latest_file}")
        return latest_file
    
    def load_data(self) -> bool:
        """加载ROE数据"""
        try:
            print(f"📊 加载ROE数据进行因子回测...")
            self.df = pd.read_csv(self.data_file, parse_dates=['date'])
            
            # 数据预处理
            self.df = self.df.sort_values(['stock_code', 'date']).reset_index(drop=True)
            
            # 过滤异常值
            self.df = self.df[
                (self.df['roe'] >= -100) & 
                (self.df['roe'] <= 200)
            ].copy()
            
            print(f"✅ 数据加载成功")
            print(f"   - 总记录数: {len(self.df):,}")
            print(f"   - 股票数量: {self.df['stock_code'].nunique()}")
            print(f"   - 日期范围: {self.df['date'].min().date()} 到 {self.df['date'].max().date()}")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def prepare_monthly_data(self, start_date: str = "2015-01-01", end_date: str = "2024-12-31") -> pd.DataFrame:
        """准备月度回测数据"""
        print(f"\n📅 准备月度回测数据: {start_date} 到 {end_date}")
        
        # 筛选日期范围
        start_date = pd.to_datetime(start_date)
        end_date = pd.to_datetime(end_date)
        
        backtest_data = self.df[
            (self.df['date'] >= start_date) & 
            (self.df['date'] <= end_date)
        ].copy()
        
        # 生成月末数据
        backtest_data['year_month'] = backtest_data['date'].dt.to_period('M')
        monthly_data = backtest_data.groupby(['stock_code', 'year_month']).agg({
            'stock_name': 'first',
            'roe': 'last',  # 使用月末ROE
            'date': 'last'
        }).reset_index()
        
        print(f"   - 月度数据记录数: {len(monthly_data):,}")
        print(f"   - 月份数量: {monthly_data['year_month'].nunique()}")
        
        return monthly_data
    
    def create_roe_portfolios(self, monthly_data: pd.DataFrame, n_groups: int = 5) -> Dict[str, pd.DataFrame]:
        """创建ROE分组投资组合回测"""
        print(f"\n🔄 开始ROE因子分组回测...")
        
        portfolio_returns = {f'Group_{i+1}': [] for i in range(n_groups)}
        
        # 按月份进行回测
        months = sorted(monthly_data['year_month'].unique())
        
        for i, month in enumerate(months[:-1]):  # 排除最后一个月
            next_month = months[i + 1]
            
            print(f"📊 处理月份: {month}")
            
            # 当月数据用于排名
            current_month_data = monthly_data[monthly_data['year_month'] == month].copy()
            
            if len(current_month_data) < n_groups:
                continue
            
            # 按ROE排序并分组
            current_month_data = current_month_data.sort_values('roe', ascending=False).reset_index(drop=True)
            group_size = len(current_month_data) // n_groups
            
            # 下月数据用于计算收益
            next_month_data = monthly_data[monthly_data['year_month'] == next_month]
            
            for group_idx in range(n_groups):
                start_idx = group_idx * group_size
                if group_idx == n_groups - 1:  # 最后一组包含剩余股票
                    end_idx = len(current_month_data)
                else:
                    end_idx = (group_idx + 1) * group_size
                
                # 当前组合的股票
                group_stocks = current_month_data.iloc[start_idx:end_idx]['stock_code'].tolist()
                
                # 计算组合收益（简化：使用ROE变化作为收益代理）
                group_return = self.calculate_group_return(
                    group_stocks, current_month_data, next_month_data
                )
                
                portfolio_returns[f'Group_{group_idx+1}'].append({
                    'month': month,
                    'return': group_return,
                    'stocks_count': len(group_stocks),
                    'avg_roe': current_month_data.iloc[start_idx:end_idx]['roe'].mean()
                })
        
        # 转换为DataFrame
        results = {}
        for group_name in portfolio_returns:
            if portfolio_returns[group_name]:
                results[group_name] = pd.DataFrame(portfolio_returns[group_name])
            else:
                results[group_name] = pd.DataFrame()
        
        self.backtest_results['portfolio_returns'] = results
        return results
    
    def calculate_group_return(self, group_stocks: List[str], current_data: pd.DataFrame, 
                             next_data: pd.DataFrame) -> float:
        """计算组合收益（简化模型）"""
        try:
            # 获取当前月和下月的ROE数据
            current_roe = current_data[current_data['stock_code'].isin(group_stocks)]['roe']
            next_roe = next_data[next_data['stock_code'].isin(group_stocks)]['roe']
            
            # 找到共同股票
            current_stocks = set(current_data[current_data['stock_code'].isin(group_stocks)]['stock_code'])
            next_stocks = set(next_data[next_data['stock_code'].isin(group_stocks)]['stock_code'])
            common_stocks = current_stocks.intersection(next_stocks)
            
            if not common_stocks:
                return 0.0
            
            # 计算ROE变化率作为收益代理
            returns = []
            for stock in common_stocks:
                curr_roe = current_data[current_data['stock_code'] == stock]['roe'].iloc[0]
                next_roe_val = next_data[next_data['stock_code'] == stock]['roe'].iloc[0]
                
                if curr_roe != 0:
                    # ROE改善率作为收益代理，并进行调整
                    roe_change = (next_roe_val - curr_roe) / abs(curr_roe)
                    # 转换为合理的月收益率范围
                    monthly_return = np.tanh(roe_change * 0.1)  # 使用tanh函数限制在[-1,1]范围
                    returns.append(monthly_return)
            
            return np.mean(returns) if returns else 0.0
            
        except Exception as e:
            print(f"⚠️  计算组合收益失败: {e}")
            return 0.0
    
    def calculate_performance_metrics(self) -> pd.DataFrame:
        """计算绩效指标"""
        print(f"\n📈 计算绩效指标...")
        
        if 'portfolio_returns' not in self.backtest_results:
            print("❌ 请先运行回测模拟")
            return pd.DataFrame()
        
        portfolio_returns = self.backtest_results['portfolio_returns']
        metrics = []
        
        for group_name, returns_df in portfolio_returns.items():
            if returns_df.empty:
                continue
            
            returns = returns_df['return'].values
            
            # 基本统计
            total_return = np.prod(1 + returns) - 1
            annualized_return = (1 + total_return) ** (12 / len(returns)) - 1 if len(returns) > 0 else 0
            volatility = np.std(returns) * np.sqrt(12)
            sharpe_ratio = annualized_return / volatility if volatility > 0 else 0
            
            # 最大回撤
            cumulative_returns = np.cumprod(1 + returns)
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdowns = (cumulative_returns - running_max) / running_max
            max_drawdown = np.min(drawdowns)
            
            # 胜率
            win_rate = np.sum(returns > 0) / len(returns) if len(returns) > 0 else 0
            
            metrics.append({
                'Portfolio': group_name,
                'Total_Return': total_return,
                'Annualized_Return': annualized_return,
                'Volatility': volatility,
                'Sharpe_Ratio': sharpe_ratio,
                'Max_Drawdown': max_drawdown,
                'Win_Rate': win_rate,
                'Periods': len(returns)
            })
        
        metrics_df = pd.DataFrame(metrics)
        self.backtest_results['performance_metrics'] = metrics_df
        
        return metrics_df
    
    def analyze_roe_factor_effectiveness(self) -> Dict:
        """分析ROE因子有效性"""
        print(f"\n🔍 分析ROE因子有效性...")
        
        if 'performance_metrics' not in self.backtest_results:
            self.calculate_performance_metrics()
        
        metrics_df = self.backtest_results['performance_metrics']
        
        if metrics_df.empty:
            return {}
        
        analysis = {}
        
        # 分析高ROE组合 vs 低ROE组合
        if len(metrics_df) >= 2:
            high_roe_group = metrics_df[metrics_df['Portfolio'] == 'Group_1']
            low_roe_group = metrics_df[metrics_df['Portfolio'] == f'Group_{len(metrics_df)}']
            
            if not high_roe_group.empty and not low_roe_group.empty:
                high_return = high_roe_group['Annualized_Return'].iloc[0]
                low_return = low_roe_group['Annualized_Return'].iloc[0]
                
                analysis['factor_effectiveness'] = {
                    'high_roe_return': high_return,
                    'low_roe_return': low_return,
                    'return_spread': high_return - low_return,
                    'factor_works': high_return > low_return
                }
        
        # 计算因子单调性
        returns = metrics_df['Annualized_Return'].values
        if len(returns) > 1:
            # 检查是否单调递减（高ROE组合收益更高）
            diffs = np.diff(returns)
            monotonicity = np.sum(diffs <= 0) / len(diffs) if len(diffs) > 0 else 0
            analysis['monotonicity'] = monotonicity
        
        self.backtest_results['factor_analysis'] = analysis
        return analysis
    
    def create_visualization(self, save_path: str = "roe_factor_backtest_results.png"):
        """创建可视化图表"""
        print(f"\n📊 创建回测结果可视化...")
        
        if 'portfolio_returns' not in self.backtest_results:
            print("❌ 请先运行回测模拟")
            return None
        
        portfolio_returns = self.backtest_results['portfolio_returns']
        
        # 创建2x2子图
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('HSI ROE Factor Backtest Results', fontsize=16, fontweight='bold')
        
        # 1. 累积收益曲线
        ax1 = axes[0, 0]
        for group_name, returns_df in portfolio_returns.items():
            if not returns_df.empty:
                cumulative_returns = np.cumprod(1 + returns_df['return'].values)
                ax1.plot(range(len(cumulative_returns)), cumulative_returns, 
                        label=group_name, linewidth=2)
        
        ax1.set_title('Cumulative Returns')
        ax1.set_xlabel('Months')
        ax1.set_ylabel('Cumulative Return')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 年化收益率对比
        if 'performance_metrics' in self.backtest_results:
            metrics_df = self.backtest_results['performance_metrics']
            
            ax2 = axes[0, 1]
            bars = ax2.bar(metrics_df['Portfolio'], metrics_df['Annualized_Return'] * 100)
            ax2.set_title('Annualized Returns (%)')
            ax2.set_ylabel('Annualized Return (%)')
            plt.setp(ax2.get_xticklabels(), rotation=45)
            
            # 添加数值标签
            for bar, value in zip(bars, metrics_df['Annualized_Return'] * 100):
                ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5, 
                        f'{value:.1f}%', ha='center', va='bottom')
            
            # 3. 风险收益散点图
            ax3 = axes[1, 0]
            scatter = ax3.scatter(metrics_df['Volatility'] * 100, 
                                metrics_df['Annualized_Return'] * 100,
                                s=100, alpha=0.7)
            
            for i, row in metrics_df.iterrows():
                ax3.annotate(row['Portfolio'], 
                           (row['Volatility'] * 100, row['Annualized_Return'] * 100),
                           xytext=(5, 5), textcoords='offset points')
            
            ax3.set_title('Risk-Return Profile')
            ax3.set_xlabel('Volatility (%)')
            ax3.set_ylabel('Annualized Return (%)')
            ax3.grid(True, alpha=0.3)
            
            # 4. 最大回撤
            ax4 = axes[1, 1]
            bars = ax4.bar(metrics_df['Portfolio'], metrics_df['Max_Drawdown'] * 100, color='red', alpha=0.7)
            ax4.set_title('Maximum Drawdown (%)')
            ax4.set_ylabel('Max Drawdown (%)')
            plt.setp(ax4.get_xticklabels(), rotation=45)
            
            # 添加数值标签
            for bar, value in zip(bars, metrics_df['Max_Drawdown'] * 100):
                ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() - 0.5, 
                        f'{value:.1f}%', ha='center', va='top')
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"✅ 可视化结果已保存: {save_path}")
        plt.show()
        
        return save_path

    def generate_backtest_report(self, output_file: str = None) -> str:
        """生成回测报告"""
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"roe_factor_backtest_report_{timestamp}.md"

        print(f"\n📋 生成回测报告...")

        report_lines = []
        report_lines.append("# 恒生指数ROE因子回测报告")
        report_lines.append(f"## 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")

        # 回测设置
        report_lines.append("## 📊 回测设置")
        report_lines.append(f"- 调仓频率: 月度")
        report_lines.append(f"- 分组数量: 5组")
        report_lines.append(f"- 权重方式: 等权重")
        report_lines.append(f"- 收益计算: ROE变化率代理")
        report_lines.append("")

        # 绩效指标
        if 'performance_metrics' in self.backtest_results:
            metrics_df = self.backtest_results['performance_metrics']

            report_lines.append("## 📈 绩效指标")
            report_lines.append("| 投资组合 | 年化收益率(%) | 波动率(%) | 夏普比率 | 最大回撤(%) | 胜率(%) |")
            report_lines.append("|---------|-------------|----------|---------|-----------|--------|")

            for _, row in metrics_df.iterrows():
                report_lines.append(
                    f"| {row['Portfolio']} | {row['Annualized_Return']*100:.2f} | "
                    f"{row['Volatility']*100:.2f} | {row['Sharpe_Ratio']:.2f} | "
                    f"{row['Max_Drawdown']*100:.2f} | {row['Win_Rate']*100:.2f} |"
                )

            report_lines.append("")

        # 因子有效性分析
        if 'factor_analysis' in self.backtest_results:
            analysis = self.backtest_results['factor_analysis']

            report_lines.append("## 🔍 因子有效性分析")

            if 'factor_effectiveness' in analysis:
                eff = analysis['factor_effectiveness']
                report_lines.append(f"### ROE因子效果")
                report_lines.append(f"- 高ROE组合年化收益: {eff['high_roe_return']*100:.2f}%")
                report_lines.append(f"- 低ROE组合年化收益: {eff['low_roe_return']*100:.2f}%")
                report_lines.append(f"- 收益差: {eff['return_spread']*100:.2f}%")
                report_lines.append(f"- 因子有效性: {'✅ 有效' if eff['factor_works'] else '❌ 无效'}")
                report_lines.append("")

            if 'monotonicity' in analysis:
                mono = analysis['monotonicity']
                report_lines.append(f"### 单调性分析")
                report_lines.append(f"- 单调性指标: {mono:.2f}")
                report_lines.append(f"- 单调性评价: {'✅ 良好' if mono > 0.6 else '⚠️ 一般' if mono > 0.4 else '❌ 较差'}")
                report_lines.append("")

        # 投资建议
        report_lines.append("## 💡 投资建议")

        if 'performance_metrics' in self.backtest_results:
            metrics_df = self.backtest_results['performance_metrics']

            if not metrics_df.empty:
                # 找出最佳组合
                best_sharpe = metrics_df.loc[metrics_df['Sharpe_Ratio'].idxmax()]
                best_return = metrics_df.loc[metrics_df['Annualized_Return'].idxmax()]

                report_lines.append(f"### 推荐策略")
                report_lines.append(f"- 最佳夏普比率组合: {best_sharpe['Portfolio']} (夏普比率: {best_sharpe['Sharpe_Ratio']:.2f})")
                report_lines.append(f"- 最高收益组合: {best_return['Portfolio']} (年化收益: {best_return['Annualized_Return']*100:.2f}%)")
                report_lines.append("")

                # 风险提示
                report_lines.append(f"### 风险提示")
                max_dd = metrics_df['Max_Drawdown'].min()
                report_lines.append(f"- 最大回撤风险: {max_dd*100:.2f}%")

                if max_dd < -0.2:
                    report_lines.append("- ⚠️ 回撤风险较高，建议控制仓位")
                elif max_dd < -0.1:
                    report_lines.append("- ⚠️ 回撤风险中等，建议适度配置")
                else:
                    report_lines.append("- ✅ 回撤风险可控")

                report_lines.append("")
                report_lines.append("### 注意事项")
                report_lines.append("- 本回测使用ROE变化率作为收益代理，实际投资需考虑股价变动")
                report_lines.append("- 回测结果不代表未来表现，投资需谨慎")
                report_lines.append("- 建议结合其他因子进行多因子投资策略")

        # 保存报告
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))

        print(f"✅ 回测报告已生成: {output_file}")
        return output_file


def main():
    """主函数"""
    print("🚀 恒生指数ROE因子回测工具（简化版）")
    print("=" * 60)

    # 创建回测器
    backtester = HSIROEFactorBacktestSimple()

    # 加载数据
    if not backtester.load_data():
        return

    # 设置回测参数
    print("\n⚙️ 回测参数设置:")
    print("1. 默认设置 (2015-2024, 月度调仓, 5分组)")
    print("2. 自定义设置")

    choice = input("请选择 (1-2): ").strip()

    if choice == "2":
        start_date = input("请输入开始日期 (YYYY-MM-DD, 默认2015-01-01): ").strip()
        end_date = input("请输入结束日期 (YYYY-MM-DD, 默认2024-12-31): ").strip()
        n_groups = input("请输入分组数量 (默认5): ").strip()

        start_date = start_date if start_date else "2015-01-01"
        end_date = end_date if end_date else "2024-12-31"
        n_groups = int(n_groups) if n_groups.isdigit() else 5
    else:
        start_date = "2015-01-01"
        end_date = "2024-12-31"
        n_groups = 5

    print(f"\n📅 回测设置:")
    print(f"   - 开始日期: {start_date}")
    print(f"   - 结束日期: {end_date}")
    print(f"   - 分组数量: {n_groups}")

    # 准备回测数据
    monthly_data = backtester.prepare_monthly_data(start_date, end_date)

    # 运行回测
    portfolio_returns = backtester.create_roe_portfolios(monthly_data, n_groups)

    # 计算绩效指标
    metrics_df = backtester.calculate_performance_metrics()

    if not metrics_df.empty:
        print(f"\n📊 回测结果摘要:")
        print(metrics_df[['Portfolio', 'Annualized_Return', 'Volatility', 'Sharpe_Ratio', 'Max_Drawdown']].to_string(index=False))

    # 分析因子有效性
    factor_analysis = backtester.analyze_roe_factor_effectiveness()

    if factor_analysis:
        print(f"\n🔍 ROE因子有效性分析:")
        if 'factor_effectiveness' in factor_analysis:
            eff = factor_analysis['factor_effectiveness']
            print(f"   - 高ROE vs 低ROE收益差: {eff['return_spread']*100:.2f}%")
            print(f"   - 因子有效性: {'✅ 有效' if eff['factor_works'] else '❌ 无效'}")

        if 'monotonicity' in factor_analysis:
            mono = factor_analysis['monotonicity']
            print(f"   - 单调性: {mono:.2f}")

    # 生成可视化
    viz_file = backtester.create_visualization()

    # 生成报告
    report_file = backtester.generate_backtest_report()

    print(f"\n🎉 ROE因子回测完成！")
    print(f"📊 可视化结果: {viz_file}")
    print(f"📋 回测报告: {report_file}")


if __name__ == "__main__":
    main()
