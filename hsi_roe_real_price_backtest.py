#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
恒生指数成分股ROE因子回测工具（真实股价版）

功能：
1. 使用真实股价数据计算收益
2. 最近5年回测（2020-2024）
3. ROE因子有效性验证
4. 详细绩效分析

作者: AI Assistant
创建时间: 2025年1月
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
import os
from typing import Dict, List, Tuple, Optional
import akshare as ak
import time

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class HSIROERealPriceBacktest:
    """恒生指数ROE因子回测器（真实股价版）"""
    
    def __init__(self, data_file: str = None):
        self.data_file = data_file
        self.roe_df = None
        self.price_data = {}
        self.backtest_results = {}
        
        # 如果没有指定文件，自动查找最新的合并数据文件
        if data_file is None:
            self.data_file = self.find_latest_combined_file()
    
    def find_latest_combined_file(self) -> str:
        """查找最新的合并数据文件"""
        cache_dir = "hsi_roe_cache"
        if not os.path.exists(cache_dir):
            raise FileNotFoundError("未找到ROE数据缓存目录")
        
        combined_files = [f for f in os.listdir(cache_dir) if f.startswith('hsi_daily_roe_combined_')]
        
        if not combined_files:
            raise FileNotFoundError("未找到ROE合并数据文件")
        
        combined_files.sort(reverse=True)
        latest_file = os.path.join(cache_dir, combined_files[0])
        
        print(f"📁 使用ROE数据文件: {latest_file}")
        return latest_file
    
    def load_roe_data(self) -> bool:
        """加载ROE数据"""
        try:
            print(f"📊 加载ROE数据...")
            self.roe_df = pd.read_csv(self.data_file, parse_dates=['date'])
            
            # 数据预处理
            self.roe_df = self.roe_df.sort_values(['stock_code', 'date']).reset_index(drop=True)
            
            # 过滤异常值
            self.roe_df = self.roe_df[
                (self.roe_df['roe'] >= -100) & 
                (self.roe_df['roe'] <= 200)
            ].copy()
            
            # 筛选最近5年数据
            end_date = datetime.now()
            start_date = end_date - timedelta(days=5*365)
            
            self.roe_df = self.roe_df[
                (self.roe_df['date'] >= start_date) & 
                (self.roe_df['date'] <= end_date)
            ].copy()
            
            print(f"✅ ROE数据加载成功")
            print(f"   - 总记录数: {len(self.roe_df):,}")
            print(f"   - 股票数量: {self.roe_df['stock_code'].nunique()}")
            print(f"   - 日期范围: {self.roe_df['date'].min().date()} 到 {self.roe_df['date'].max().date()}")
            
            return True
            
        except Exception as e:
            print(f"❌ ROE数据加载失败: {e}")
            return False
    
    def download_price_data(self) -> bool:
        """下载股价数据（使用akshare）"""
        print(f"\n💰 开始下载港股价数据（使用akshare）...")

        # 获取所有股票代码
        stock_codes = self.roe_df['stock_code'].unique()

        # 设置日期范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=5*365 + 30)  # 多下载一个月的数据

        success_count = 0
        failed_stocks = []

        for i, stock_code in enumerate(stock_codes):
            try:
                stock_code_str = str(stock_code).zfill(5)  # 转换为字符串并补齐到5位
                print(f"📈 下载 {stock_code_str} 股价数据 ({i+1}/{len(stock_codes)})")

                # 使用akshare下载港股数据
                hist = ak.stock_hk_hist(
                    symbol=stock_code_str,
                    period="daily",
                    start_date=start_date.strftime('%Y%m%d'),
                    end_date=end_date.strftime('%Y%m%d'),
                    adjust="qfq"  # 前复权
                )

                if not hist.empty:
                    # 重命名列并处理数据
                    hist = hist.rename(columns={
                        '日期': 'date',
                        '收盘': 'close'
                    })

                    # 确保日期格式正确
                    hist['date'] = pd.to_datetime(hist['date']).dt.date
                    hist['close'] = pd.to_numeric(hist['close'], errors='coerce')

                    # 过滤无效数据
                    hist = hist.dropna(subset=['close'])

                    if not hist.empty:
                        # 存储价格数据
                        self.price_data[stock_code_str] = hist[['date', 'close']].copy()
                        success_count += 1
                        print(f"✅ {stock_code_str} 下载成功，{len(hist)} 条记录")
                    else:
                        failed_stocks.append(stock_code_str)
                        print(f"⚠️  {stock_code_str} 数据为空")
                else:
                    failed_stocks.append(stock_code_str)
                    print(f"⚠️  {stock_code_str} 无数据")

                # 避免请求过于频繁
                time.sleep(0.5)

            except Exception as e:
                failed_stocks.append(str(stock_code))
                print(f"❌ {stock_code} 下载失败: {e}")
                # 继续下载其他股票
                continue

        print(f"\n✅ 股价数据下载完成")
        print(f"   - 成功: {success_count}/{len(stock_codes)} 只股票")
        if failed_stocks:
            print(f"   - 失败股票数量: {len(failed_stocks)}")
            if len(failed_stocks) <= 10:
                print(f"   - 失败股票: {failed_stocks}")

        return success_count > 0
    
    def prepare_monthly_data(self, start_date: str = "2020-01-01", end_date: str = "2024-12-31") -> pd.DataFrame:
        """准备月度回测数据"""
        print(f"\n📅 准备月度回测数据: {start_date} 到 {end_date}")
        
        # 筛选日期范围
        start_date = pd.to_datetime(start_date)
        end_date = pd.to_datetime(end_date)
        
        backtest_data = self.roe_df[
            (self.roe_df['date'] >= start_date) & 
            (self.roe_df['date'] <= end_date)
        ].copy()
        
        # 生成月末数据
        backtest_data['year_month'] = backtest_data['date'].dt.to_period('M')
        monthly_roe = backtest_data.groupby(['stock_code', 'year_month']).agg({
            'stock_name': 'first',
            'roe': 'last',  # 使用月末ROE
            'date': 'last'
        }).reset_index()
        
        print(f"   - 月度ROE数据记录数: {len(monthly_roe):,}")
        print(f"   - 月份数量: {monthly_roe['year_month'].nunique()}")
        
        return monthly_roe
    
    def get_stock_price(self, stock_code: str, date: pd.Timestamp) -> float:
        """获取指定日期的股价"""
        if stock_code not in self.price_data:
            return None
        
        price_df = self.price_data[stock_code]
        price_df['date'] = pd.to_datetime(price_df['date'])
        
        # 查找最接近的日期
        target_date = date.date()
        price_df['date_diff'] = abs((price_df['date'].dt.date - target_date).dt.days)
        
        # 找到最近的交易日（不超过5天）
        closest_data = price_df[price_df['date_diff'] <= 5]
        
        if closest_data.empty:
            return None
        
        closest_row = closest_data.loc[closest_data['date_diff'].idxmin()]
        return closest_row['close']
    
    def calculate_real_returns(self, monthly_roe: pd.DataFrame, n_groups: int = 5) -> Dict[str, pd.DataFrame]:
        """使用真实股价计算投资组合收益"""
        print(f"\n🔄 开始真实股价ROE因子回测...")
        
        portfolio_returns = {f'Group_{i+1}': [] for i in range(n_groups)}
        portfolio_holdings = {f'Group_{i+1}': [] for i in range(n_groups)}
        
        # 按月份进行回测
        months = sorted(monthly_roe['year_month'].unique())
        
        for i, month in enumerate(months[:-1]):  # 排除最后一个月
            next_month = months[i + 1]
            
            print(f"📊 处理月份: {month}")
            
            # 当月数据用于排名
            current_month_data = monthly_roe[monthly_roe['year_month'] == month].copy()
            
            if len(current_month_data) < n_groups:
                continue
            
            # 按ROE排序并分组
            current_month_data = current_month_data.sort_values('roe', ascending=False).reset_index(drop=True)
            group_size = len(current_month_data) // n_groups
            
            # 获取当月月末日期（买入日期）
            buy_date = pd.to_datetime(f"{month}-01") + pd.offsets.MonthEnd(0)
            
            # 获取下月月末日期（卖出日期）
            sell_date = pd.to_datetime(f"{next_month}-01") + pd.offsets.MonthEnd(0)
            
            for group_idx in range(n_groups):
                start_idx = group_idx * group_size
                if group_idx == n_groups - 1:  # 最后一组包含剩余股票
                    end_idx = len(current_month_data)
                else:
                    end_idx = (group_idx + 1) * group_size
                
                # 当前组合的股票
                group_stocks = current_month_data.iloc[start_idx:end_idx]['stock_code'].tolist()
                
                # 计算组合真实收益
                group_return = self.calculate_portfolio_real_return(
                    group_stocks, buy_date, sell_date
                )
                
                portfolio_returns[f'Group_{group_idx+1}'].append({
                    'month': month,
                    'return': group_return,
                    'stocks_count': len(group_stocks),
                    'avg_roe': current_month_data.iloc[start_idx:end_idx]['roe'].mean()
                })
                
                portfolio_holdings[f'Group_{group_idx+1}'].append({
                    'month': month,
                    'stocks': group_stocks,
                    'avg_roe': current_month_data.iloc[start_idx:end_idx]['roe'].mean()
                })
        
        # 转换为DataFrame
        results = {}
        for group_name in portfolio_returns:
            if portfolio_returns[group_name]:
                results[group_name] = pd.DataFrame(portfolio_returns[group_name])
            else:
                results[group_name] = pd.DataFrame()
        
        self.backtest_results['portfolio_returns'] = results
        self.backtest_results['portfolio_holdings'] = portfolio_holdings
        
        return results
    
    def calculate_portfolio_real_return(self, group_stocks: List[str], buy_date: pd.Timestamp, 
                                      sell_date: pd.Timestamp) -> float:
        """计算投资组合真实收益率"""
        try:
            returns = []
            
            for stock_code in group_stocks:
                # 获取买入价格
                buy_price = self.get_stock_price(stock_code, buy_date)
                
                # 获取卖出价格
                sell_price = self.get_stock_price(stock_code, sell_date)
                
                if buy_price is not None and sell_price is not None and buy_price > 0:
                    stock_return = (sell_price - buy_price) / buy_price
                    returns.append(stock_return)
            
            # 等权重平均收益
            if returns:
                return np.mean(returns)
            else:
                return 0.0
                
        except Exception as e:
            print(f"⚠️  计算组合收益失败: {e}")
            return 0.0
    
    def calculate_performance_metrics(self) -> pd.DataFrame:
        """计算绩效指标"""
        print(f"\n📈 计算绩效指标...")
        
        if 'portfolio_returns' not in self.backtest_results:
            print("❌ 请先运行回测模拟")
            return pd.DataFrame()
        
        portfolio_returns = self.backtest_results['portfolio_returns']
        metrics = []
        
        for group_name, returns_df in portfolio_returns.items():
            if returns_df.empty:
                continue
            
            returns = returns_df['return'].values
            
            # 基本统计
            total_return = np.prod(1 + returns) - 1
            annualized_return = (1 + total_return) ** (12 / len(returns)) - 1 if len(returns) > 0 else 0
            volatility = np.std(returns) * np.sqrt(12)
            sharpe_ratio = annualized_return / volatility if volatility > 0 else 0
            
            # 最大回撤
            cumulative_returns = np.cumprod(1 + returns)
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdowns = (cumulative_returns - running_max) / running_max
            max_drawdown = np.min(drawdowns)
            
            # 胜率
            win_rate = np.sum(returns > 0) / len(returns) if len(returns) > 0 else 0
            
            metrics.append({
                'Portfolio': group_name,
                'Total_Return': total_return,
                'Annualized_Return': annualized_return,
                'Volatility': volatility,
                'Sharpe_Ratio': sharpe_ratio,
                'Max_Drawdown': max_drawdown,
                'Win_Rate': win_rate,
                'Periods': len(returns)
            })
        
        metrics_df = pd.DataFrame(metrics)
        self.backtest_results['performance_metrics'] = metrics_df
        
        return metrics_df

    def analyze_roe_factor_effectiveness(self) -> Dict:
        """分析ROE因子有效性"""
        print(f"\n🔍 分析ROE因子有效性...")

        if 'performance_metrics' not in self.backtest_results:
            self.calculate_performance_metrics()

        metrics_df = self.backtest_results['performance_metrics']

        if metrics_df.empty:
            return {}

        analysis = {}

        # 分析高ROE组合 vs 低ROE组合
        if len(metrics_df) >= 2:
            high_roe_group = metrics_df[metrics_df['Portfolio'] == 'Group_1']
            low_roe_group = metrics_df[metrics_df['Portfolio'] == f'Group_{len(metrics_df)}']

            if not high_roe_group.empty and not low_roe_group.empty:
                high_return = high_roe_group['Annualized_Return'].iloc[0]
                low_return = low_roe_group['Annualized_Return'].iloc[0]

                analysis['factor_effectiveness'] = {
                    'high_roe_return': high_return,
                    'low_roe_return': low_return,
                    'return_spread': high_return - low_return,
                    'factor_works': high_return > low_return
                }

        # 计算因子单调性
        returns = metrics_df['Annualized_Return'].values
        if len(returns) > 1:
            # 检查是否单调递减（高ROE组合收益更高）
            diffs = np.diff(returns)
            monotonicity = np.sum(diffs <= 0) / len(diffs) if len(diffs) > 0 else 0
            analysis['monotonicity'] = monotonicity

        # 计算信息系数（IC）
        ic_analysis = self.calculate_information_coefficient()
        analysis['ic_analysis'] = ic_analysis

        self.backtest_results['factor_analysis'] = analysis
        return analysis

    def calculate_information_coefficient(self) -> Dict:
        """计算信息系数"""
        try:
            if 'portfolio_returns' not in self.backtest_results:
                return {}

            portfolio_returns = self.backtest_results['portfolio_returns']
            portfolio_holdings = self.backtest_results['portfolio_holdings']

            ic_values = []

            # 对每个调仓期计算IC
            for group_name in portfolio_returns:
                returns_df = portfolio_returns[group_name]
                holdings = portfolio_holdings[group_name]

                for i, (_, row) in enumerate(returns_df.iterrows()):
                    if i < len(holdings):
                        period_return = row['return']
                        avg_roe = holdings[i]['avg_roe']
                        ic_values.append({'roe': avg_roe, 'return': period_return})

            if not ic_values:
                return {}

            ic_df = pd.DataFrame(ic_values)

            # 计算相关系数
            correlation = ic_df['roe'].corr(ic_df['return'])

            return {
                'ic_mean': correlation,
                'ic_std': ic_df.groupby(ic_df.index // 5)['roe'].corr(ic_df.groupby(ic_df.index // 5)['return']).std(),
                'ic_ir': correlation / (ic_df.groupby(ic_df.index // 5)['roe'].corr(ic_df.groupby(ic_df.index // 5)['return']).std() + 1e-6),
                'samples': len(ic_values)
            }

        except Exception as e:
            print(f"⚠️  计算IC失败: {e}")
            return {}

    def create_visualization(self, save_path: str = "hsi_roe_real_price_backtest_results.png"):
        """创建可视化图表"""
        print(f"\n📊 创建回测结果可视化...")

        if 'portfolio_returns' not in self.backtest_results:
            print("❌ 请先运行回测模拟")
            return None

        portfolio_returns = self.backtest_results['portfolio_returns']

        # 创建2x2子图
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('HSI ROE Factor Backtest Results (Real Price Data 2020-2024)', fontsize=16, fontweight='bold')

        # 1. 累积收益曲线
        ax1 = axes[0, 0]
        colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']

        for i, (group_name, returns_df) in enumerate(portfolio_returns.items()):
            if not returns_df.empty:
                cumulative_returns = np.cumprod(1 + returns_df['return'].values)
                months = range(len(cumulative_returns))
                ax1.plot(months, cumulative_returns,
                        label=group_name, linewidth=2, color=colors[i % len(colors)])

        ax1.set_title('Cumulative Returns')
        ax1.set_xlabel('Months')
        ax1.set_ylabel('Cumulative Return')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 2. 年化收益率对比
        if 'performance_metrics' in self.backtest_results:
            metrics_df = self.backtest_results['performance_metrics']

            ax2 = axes[0, 1]
            bars = ax2.bar(metrics_df['Portfolio'], metrics_df['Annualized_Return'] * 100,
                          color=colors[:len(metrics_df)])
            ax2.set_title('Annualized Returns (%)')
            ax2.set_ylabel('Annualized Return (%)')
            plt.setp(ax2.get_xticklabels(), rotation=45)

            # 添加数值标签
            for bar, value in zip(bars, metrics_df['Annualized_Return'] * 100):
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2, height + (1 if height >= 0 else -3),
                        f'{value:.1f}%', ha='center', va='bottom' if height >= 0 else 'top')

            # 3. 风险收益散点图
            ax3 = axes[1, 0]
            scatter = ax3.scatter(metrics_df['Volatility'] * 100,
                                metrics_df['Annualized_Return'] * 100,
                                s=100, alpha=0.7, c=colors[:len(metrics_df)])

            for i, row in metrics_df.iterrows():
                ax3.annotate(row['Portfolio'],
                           (row['Volatility'] * 100, row['Annualized_Return'] * 100),
                           xytext=(5, 5), textcoords='offset points')

            ax3.set_title('Risk-Return Profile')
            ax3.set_xlabel('Volatility (%)')
            ax3.set_ylabel('Annualized Return (%)')
            ax3.grid(True, alpha=0.3)

            # 4. 夏普比率对比
            ax4 = axes[1, 1]
            bars = ax4.bar(metrics_df['Portfolio'], metrics_df['Sharpe_Ratio'],
                          color=colors[:len(metrics_df)])
            ax4.set_title('Sharpe Ratio')
            ax4.set_ylabel('Sharpe Ratio')
            plt.setp(ax4.get_xticklabels(), rotation=45)

            # 添加数值标签
            for bar, value in zip(bars, metrics_df['Sharpe_Ratio']):
                height = bar.get_height()
                ax4.text(bar.get_x() + bar.get_width()/2, height + (0.05 if height >= 0 else -0.1),
                        f'{value:.2f}', ha='center', va='bottom' if height >= 0 else 'top')

        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"✅ 可视化结果已保存: {save_path}")
        plt.show()

        return save_path

    def generate_detailed_report(self, output_file: str = None) -> str:
        """生成详细回测报告"""
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"hsi_roe_real_price_backtest_report_{timestamp}.md"

        print(f"\n📋 生成详细回测报告...")

        report_lines = []
        report_lines.append("# 恒生指数ROE因子回测报告（真实股价版）")
        report_lines.append(f"## 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")

        # 回测设置
        report_lines.append("## 📊 回测设置")
        report_lines.append(f"- 回测期间: 2020-2024年（最近5年）")
        report_lines.append(f"- 调仓频率: 月度")
        report_lines.append(f"- 分组数量: 5组")
        report_lines.append(f"- 权重方式: 等权重")
        report_lines.append(f"- 收益计算: 真实股价数据")
        report_lines.append(f"- 数据来源: Yahoo Finance")
        report_lines.append("")

        # 数据统计
        report_lines.append("## 📈 数据统计")
        report_lines.append(f"- ROE数据股票数: {self.roe_df['stock_code'].nunique()}")
        report_lines.append(f"- 股价数据股票数: {len(self.price_data)}")
        report_lines.append(f"- 数据覆盖率: {len(self.price_data)/self.roe_df['stock_code'].nunique()*100:.1f}%")
        report_lines.append("")

        # 绩效指标
        if 'performance_metrics' in self.backtest_results:
            metrics_df = self.backtest_results['performance_metrics']

            report_lines.append("## 📈 绩效指标")
            report_lines.append("| 投资组合 | 年化收益率(%) | 波动率(%) | 夏普比率 | 最大回撤(%) | 胜率(%) | 调仓次数 |")
            report_lines.append("|---------|-------------|----------|---------|-----------|--------|--------|")

            for _, row in metrics_df.iterrows():
                report_lines.append(
                    f"| {row['Portfolio']} | {row['Annualized_Return']*100:.2f} | "
                    f"{row['Volatility']*100:.2f} | {row['Sharpe_Ratio']:.2f} | "
                    f"{row['Max_Drawdown']*100:.2f} | {row['Win_Rate']*100:.2f} | {row['Periods']} |"
                )

            report_lines.append("")

        # 因子有效性分析
        if 'factor_analysis' in self.backtest_results:
            analysis = self.backtest_results['factor_analysis']

            report_lines.append("## 🔍 因子有效性分析")

            if 'factor_effectiveness' in analysis:
                eff = analysis['factor_effectiveness']
                report_lines.append(f"### ROE因子效果")
                report_lines.append(f"- 高ROE组合年化收益: {eff['high_roe_return']*100:.2f}%")
                report_lines.append(f"- 低ROE组合年化收益: {eff['low_roe_return']*100:.2f}%")
                report_lines.append(f"- 收益差: {eff['return_spread']*100:.2f}%")
                report_lines.append(f"- 因子有效性: {'✅ 有效' if eff['factor_works'] else '❌ 无效'}")
                report_lines.append("")

            if 'monotonicity' in analysis:
                mono = analysis['monotonicity']
                report_lines.append(f"### 单调性分析")
                report_lines.append(f"- 单调性指标: {mono:.2f}")
                report_lines.append(f"- 单调性评价: {'✅ 良好' if mono > 0.6 else '⚠️ 一般' if mono > 0.4 else '❌ 较差'}")
                report_lines.append("")

            if 'ic_analysis' in analysis and analysis['ic_analysis']:
                ic = analysis['ic_analysis']
                report_lines.append(f"### 信息系数(IC)分析")
                report_lines.append(f"- IC均值: {ic['ic_mean']:.3f}")
                if 'ic_ir' in ic:
                    report_lines.append(f"- IC信息比率: {ic['ic_ir']:.3f}")
                report_lines.append(f"- 样本数量: {ic['samples']}")
                report_lines.append("")

        # 投资建议
        report_lines.append("## 💡 投资建议")

        if 'performance_metrics' in self.backtest_results:
            metrics_df = self.backtest_results['performance_metrics']

            if not metrics_df.empty:
                # 找出最佳组合
                best_sharpe = metrics_df.loc[metrics_df['Sharpe_Ratio'].idxmax()]
                best_return = metrics_df.loc[metrics_df['Annualized_Return'].idxmax()]

                report_lines.append(f"### 推荐策略")
                report_lines.append(f"- 最佳夏普比率组合: {best_sharpe['Portfolio']} (夏普比率: {best_sharpe['Sharpe_Ratio']:.2f})")
                report_lines.append(f"- 最高收益组合: {best_return['Portfolio']} (年化收益: {best_return['Annualized_Return']*100:.2f}%)")
                report_lines.append("")

                # 风险提示
                report_lines.append(f"### 风险提示")
                max_dd = metrics_df['Max_Drawdown'].min()
                report_lines.append(f"- 最大回撤风险: {max_dd*100:.2f}%")

                if max_dd < -0.3:
                    report_lines.append("- ⚠️ 回撤风险很高，建议谨慎投资")
                elif max_dd < -0.2:
                    report_lines.append("- ⚠️ 回撤风险较高，建议控制仓位")
                elif max_dd < -0.1:
                    report_lines.append("- ⚠️ 回撤风险中等，建议适度配置")
                else:
                    report_lines.append("- ✅ 回撤风险可控")

                report_lines.append("")
                report_lines.append("### 结论")

                # 根据因子有效性给出结论
                if 'factor_analysis' in self.backtest_results:
                    analysis = self.backtest_results['factor_analysis']
                    if 'factor_effectiveness' in analysis:
                        eff = analysis['factor_effectiveness']
                        if eff['factor_works']:
                            report_lines.append("- ✅ ROE因子在恒生指数中显示正向有效性")
                            report_lines.append("- 建议优先配置高ROE股票")
                        else:
                            report_lines.append("- ❌ ROE因子在恒生指数中显示反向效应")
                            report_lines.append("- 可能存在价值陷阱，高ROE股票估值过高")
                            report_lines.append("- 建议结合估值指标进行筛选")

                report_lines.append("- 建议结合其他因子进行多因子投资策略")
                report_lines.append("- 定期调整投资组合，控制风险")

        # 保存报告
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))

        print(f"✅ 详细回测报告已生成: {output_file}")
        return output_file


def main():
    """主函数"""
    print("🚀 恒生指数ROE因子回测工具（真实股价版 - 最近5年）")
    print("=" * 70)

    # 创建回测器
    backtester = HSIROERealPriceBacktest()

    # 加载ROE数据
    if not backtester.load_roe_data():
        return

    # 下载股价数据
    print("\n⚠️  注意：股价数据下载可能需要几分钟时间...")
    if not backtester.download_price_data():
        print("❌ 股价数据下载失败，无法进行回测")
        return

    # 设置回测参数
    print("\n⚙️ 回测参数设置:")
    print("1. 默认设置 (2020-2024, 月度调仓, 5分组)")
    print("2. 自定义设置")

    choice = input("请选择 (1-2): ").strip()

    if choice == "2":
        start_date = input("请输入开始日期 (YYYY-MM-DD, 默认2020-01-01): ").strip()
        end_date = input("请输入结束日期 (YYYY-MM-DD, 默认2024-12-31): ").strip()
        n_groups = input("请输入分组数量 (默认5): ").strip()

        start_date = start_date if start_date else "2020-01-01"
        end_date = end_date if end_date else "2024-12-31"
        n_groups = int(n_groups) if n_groups.isdigit() else 5
    else:
        start_date = "2020-01-01"
        end_date = "2024-12-31"
        n_groups = 5

    print(f"\n📅 回测设置:")
    print(f"   - 开始日期: {start_date}")
    print(f"   - 结束日期: {end_date}")
    print(f"   - 分组数量: {n_groups}")
    print(f"   - 使用真实股价数据")

    # 准备回测数据
    monthly_roe = backtester.prepare_monthly_data(start_date, end_date)

    # 运行回测
    print(f"\n🔄 开始回测计算，这可能需要一些时间...")
    portfolio_returns = backtester.calculate_real_returns(monthly_roe, n_groups)

    # 计算绩效指标
    metrics_df = backtester.calculate_performance_metrics()

    if not metrics_df.empty:
        print(f"\n📊 回测结果摘要:")
        display_cols = ['Portfolio', 'Annualized_Return', 'Volatility', 'Sharpe_Ratio', 'Max_Drawdown', 'Win_Rate']
        print(metrics_df[display_cols].to_string(index=False, float_format='%.4f'))

    # 分析因子有效性
    factor_analysis = backtester.analyze_roe_factor_effectiveness()

    if factor_analysis:
        print(f"\n🔍 ROE因子有效性分析:")
        if 'factor_effectiveness' in factor_analysis:
            eff = factor_analysis['factor_effectiveness']
            print(f"   - 高ROE vs 低ROE收益差: {eff['return_spread']*100:.2f}%")
            print(f"   - 因子有效性: {'✅ 有效' if eff['factor_works'] else '❌ 无效'}")

        if 'monotonicity' in factor_analysis:
            mono = factor_analysis['monotonicity']
            print(f"   - 单调性: {mono:.2f}")

        if 'ic_analysis' in factor_analysis and factor_analysis['ic_analysis']:
            ic = factor_analysis['ic_analysis']
            print(f"   - IC均值: {ic['ic_mean']:.3f}")

    # 生成可视化
    viz_file = backtester.create_visualization()

    # 生成详细报告
    report_file = backtester.generate_detailed_report()

    print(f"\n🎉 ROE因子回测完成！")
    print(f"📊 可视化结果: {viz_file}")
    print(f"📋 详细报告: {report_file}")

    # 显示关键结论
    if not metrics_df.empty:
        best_group = metrics_df.loc[metrics_df['Annualized_Return'].idxmax()]
        worst_group = metrics_df.loc[metrics_df['Annualized_Return'].idxmin()]

        print(f"\n🏆 关键结论:")
        print(f"   - 最佳组合: {best_group['Portfolio']} (年化收益: {best_group['Annualized_Return']*100:.2f}%)")
        print(f"   - 最差组合: {worst_group['Portfolio']} (年化收益: {worst_group['Annualized_Return']*100:.2f}%)")

        if factor_analysis and 'factor_effectiveness' in factor_analysis:
            eff = factor_analysis['factor_effectiveness']
            if eff['factor_works']:
                print(f"   - ✅ ROE因子有效：高ROE股票表现更好")
            else:
                print(f"   - ❌ ROE因子反向：低ROE股票表现更好")
                print(f"   - 💡 可能存在价值陷阱或均值回归效应")


if __name__ == "__main__":
    main()
