#!/bin/bash

# <PERSON><PERSON>t to activate the conda311 virtual environment
# This environment uses Python 3.11.10 from /Users/<USER>/.pyenv/versions/miniconda3-3.11-24.9.2-0

echo "Activating venv_conda311 environment..."
source venv_conda311/bin/activate

echo "Environment activated!"
echo "Python version: $(python --version)"
echo "Python path: $(which python)"
echo "Python executable: $(python -c 'import sys; print(sys.executable)')"
echo ""
echo "To deactivate, run: deactivate"
echo ""
echo "Environment is ready for use!"
