#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
恒生指数成分股ROE因子回测工具

功能：
1. ROE因子有效性回测
2. 多种ROE策略测试
3. 分组回测分析
4. 风险收益评估
5. 可视化结果展示

作者: AI Assistant
创建时间: 2025年1月
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
import os
from typing import Dict, List, Tuple, Optional
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import plotly.offline as pyo

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class HSIROEFactorBacktest:
    """恒生指数ROE因子回测器"""
    
    def __init__(self, data_file: str = None):
        self.data_file = data_file
        self.df = None
        self.backtest_results = {}
        self.rebalance_freq = 'M'  # 月度调仓
        self.lookback_period = 252  # 回看期（天）
        self.holding_period = 21   # 持有期（天）
        
        # 如果没有指定文件，自动查找最新的合并数据文件
        if data_file is None:
            self.data_file = self.find_latest_combined_file()
    
    def find_latest_combined_file(self) -> str:
        """查找最新的合并数据文件"""
        cache_dir = "hsi_roe_cache"
        if not os.path.exists(cache_dir):
            raise FileNotFoundError("未找到ROE数据缓存目录")
        
        combined_files = [f for f in os.listdir(cache_dir) if f.startswith('hsi_daily_roe_combined_')]
        
        if not combined_files:
            raise FileNotFoundError("未找到ROE合并数据文件")
        
        combined_files.sort(reverse=True)
        latest_file = os.path.join(cache_dir, combined_files[0])
        
        print(f"📁 使用数据文件: {latest_file}")
        return latest_file
    
    def load_data(self) -> bool:
        """加载ROE数据"""
        try:
            print(f"📊 加载ROE数据进行因子回测...")
            self.df = pd.read_csv(self.data_file, parse_dates=['date'])
            
            # 数据预处理
            self.df = self.df.sort_values(['stock_code', 'date']).reset_index(drop=True)
            
            # 过滤异常值
            self.df = self.df[
                (self.df['roe'] >= -100) & 
                (self.df['roe'] <= 200)
            ].copy()
            
            print(f"✅ 数据加载成功")
            print(f"   - 总记录数: {len(self.df):,}")
            print(f"   - 股票数量: {self.df['stock_code'].nunique()}")
            print(f"   - 日期范围: {self.df['date'].min().date()} 到 {self.df['date'].max().date()}")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def prepare_backtest_data(self, start_date: str = "2015-01-01", end_date: str = "2024-12-31") -> pd.DataFrame:
        """准备回测数据"""
        print(f"\n📅 准备回测数据: {start_date} 到 {end_date}")
        
        # 筛选日期范围
        start_date = pd.to_datetime(start_date)
        end_date = pd.to_datetime(end_date)
        
        backtest_data = self.df[
            (self.df['date'] >= start_date) & 
            (self.df['date'] <= end_date)
        ].copy()
        
        # 生成调仓日期
        rebalance_dates = pd.date_range(
            start=start_date, 
            end=end_date, 
            freq='MS'  # 月初调仓
        )
        
        print(f"   - 回测期间记录数: {len(backtest_data):,}")
        print(f"   - 调仓次数: {len(rebalance_dates)}")
        
        return backtest_data, rebalance_dates
    
    def calculate_roe_rankings(self, data: pd.DataFrame, date: pd.Timestamp) -> pd.DataFrame:
        """计算指定日期的ROE排名"""
        # 获取该日期的ROE数据
        date_data = data[data['date'] == date].copy()
        
        if date_data.empty:
            # 如果当天没有数据，使用最近的数据
            available_dates = data[data['date'] <= date]['date'].unique()
            if len(available_dates) == 0:
                return pd.DataFrame()
            
            latest_date = max(available_dates)
            date_data = data[data['date'] == latest_date].copy()
        
        # 计算ROE排名
        date_data = date_data.sort_values('roe', ascending=False).reset_index(drop=True)
        date_data['roe_rank'] = range(1, len(date_data) + 1)
        date_data['roe_percentile'] = date_data['roe_rank'] / len(date_data)
        
        return date_data
    
    def create_roe_portfolios(self, ranked_data: pd.DataFrame, n_groups: int = 5) -> Dict[str, List[str]]:
        """创建ROE分组投资组合"""
        if ranked_data.empty:
            return {}
        
        portfolios = {}
        group_size = len(ranked_data) // n_groups
        
        for i in range(n_groups):
            start_idx = i * group_size
            if i == n_groups - 1:  # 最后一组包含剩余所有股票
                end_idx = len(ranked_data)
            else:
                end_idx = (i + 1) * group_size
            
            group_stocks = ranked_data.iloc[start_idx:end_idx]['stock_code'].tolist()
            portfolios[f'Group_{i+1}_ROE'] = group_stocks
        
        return portfolios
    
    def simulate_portfolio_returns(self, backtest_data: pd.DataFrame, rebalance_dates: pd.DatetimeIndex, 
                                 n_groups: int = 5) -> Dict[str, pd.DataFrame]:
        """模拟投资组合收益"""
        print(f"\n🔄 开始ROE因子回测模拟...")
        
        portfolio_returns = {f'Group_{i+1}_ROE': [] for i in range(n_groups)}
        portfolio_holdings = {f'Group_{i+1}_ROE': [] for i in range(n_groups)}
        
        for i, rebalance_date in enumerate(rebalance_dates[:-1]):
            next_rebalance = rebalance_dates[i + 1]
            
            print(f"📊 处理调仓期间: {rebalance_date.date()} 到 {next_rebalance.date()}")
            
            # 获取调仓日的ROE排名
            ranked_data = self.calculate_roe_rankings(backtest_data, rebalance_date)
            
            if ranked_data.empty:
                print(f"⚠️  {rebalance_date.date()} 无可用数据，跳过")
                continue
            
            # 创建投资组合
            portfolios = self.create_roe_portfolios(ranked_data, n_groups)
            
            # 模拟持有期收益
            holding_period_data = backtest_data[
                (backtest_data['date'] >= rebalance_date) & 
                (backtest_data['date'] < next_rebalance)
            ]
            
            for group_name, stocks in portfolios.items():
                if not stocks:
                    continue
                
                # 计算组合收益（等权重）
                group_data = holding_period_data[
                    holding_period_data['stock_code'].isin(stocks)
                ]
                
                if not group_data.empty:
                    # 假设收益与ROE变化相关（简化模型）
                    # 实际应该使用股价数据，这里用ROE变化作为代理
                    period_return = self.calculate_period_return(group_data, rebalance_date, next_rebalance)
                    
                    portfolio_returns[group_name].append({
                        'date': rebalance_date,
                        'return': period_return,
                        'stocks_count': len(stocks)
                    })
                    
                    portfolio_holdings[group_name].append({
                        'date': rebalance_date,
                        'stocks': stocks,
                        'avg_roe': ranked_data[ranked_data['stock_code'].isin(stocks)]['roe'].mean()
                    })
        
        # 转换为DataFrame
        results = {}
        for group_name in portfolio_returns:
            if portfolio_returns[group_name]:
                results[group_name] = pd.DataFrame(portfolio_returns[group_name])
            else:
                results[group_name] = pd.DataFrame()
        
        self.backtest_results['portfolio_returns'] = results
        self.backtest_results['portfolio_holdings'] = portfolio_holdings
        
        return results
    
    def calculate_period_return(self, group_data: pd.DataFrame, start_date: pd.Timestamp, 
                              end_date: pd.Timestamp) -> float:
        """计算期间收益率（简化模型）"""
        try:
            # 获取期初和期末的ROE数据
            start_roe = group_data[group_data['date'] >= start_date].groupby('stock_code')['roe'].first()
            end_roe = group_data[group_data['date'] < end_date].groupby('stock_code')['roe'].last()
            
            # 计算ROE变化率作为收益代理
            common_stocks = start_roe.index.intersection(end_roe.index)
            
            if len(common_stocks) == 0:
                return 0.0
            
            roe_changes = []
            for stock in common_stocks:
                if start_roe[stock] != 0:
                    change = (end_roe[stock] - start_roe[stock]) / abs(start_roe[stock])
                    # 限制极端值
                    change = max(-0.5, min(0.5, change))
                    roe_changes.append(change)
            
            if roe_changes:
                return np.mean(roe_changes)
            else:
                return 0.0
                
        except Exception as e:
            print(f"⚠️  计算期间收益失败: {e}")
            return 0.0
    
    def calculate_performance_metrics(self) -> pd.DataFrame:
        """计算绩效指标"""
        print(f"\n📈 计算绩效指标...")
        
        if 'portfolio_returns' not in self.backtest_results:
            print("❌ 请先运行回测模拟")
            return pd.DataFrame()
        
        portfolio_returns = self.backtest_results['portfolio_returns']
        metrics = []
        
        for group_name, returns_df in portfolio_returns.items():
            if returns_df.empty:
                continue
            
            returns = returns_df['return'].values
            
            # 基本统计
            total_return = np.prod(1 + returns) - 1
            annualized_return = (1 + total_return) ** (12 / len(returns)) - 1
            volatility = np.std(returns) * np.sqrt(12)
            sharpe_ratio = annualized_return / volatility if volatility > 0 else 0
            
            # 最大回撤
            cumulative_returns = np.cumprod(1 + returns)
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdowns = (cumulative_returns - running_max) / running_max
            max_drawdown = np.min(drawdowns)
            
            # 胜率
            win_rate = np.sum(returns > 0) / len(returns) if len(returns) > 0 else 0
            
            metrics.append({
                'Portfolio': group_name,
                'Total_Return': total_return,
                'Annualized_Return': annualized_return,
                'Volatility': volatility,
                'Sharpe_Ratio': sharpe_ratio,
                'Max_Drawdown': max_drawdown,
                'Win_Rate': win_rate,
                'Periods': len(returns)
            })
        
        metrics_df = pd.DataFrame(metrics)
        self.backtest_results['performance_metrics'] = metrics_df
        
        return metrics_df
    
    def analyze_roe_factor_effectiveness(self) -> Dict:
        """分析ROE因子有效性"""
        print(f"\n🔍 分析ROE因子有效性...")
        
        if 'performance_metrics' not in self.backtest_results:
            self.calculate_performance_metrics()
        
        metrics_df = self.backtest_results['performance_metrics']
        
        if metrics_df.empty:
            return {}
        
        # 分析高ROE组合 vs 低ROE组合
        high_roe_group = metrics_df[metrics_df['Portfolio'] == 'Group_1_ROE']
        low_roe_group = metrics_df[metrics_df['Portfolio'] == 'Group_5_ROE']
        
        analysis = {}
        
        if not high_roe_group.empty and not low_roe_group.empty:
            high_return = high_roe_group['Annualized_Return'].iloc[0]
            low_return = low_roe_group['Annualized_Return'].iloc[0]
            
            analysis['factor_effectiveness'] = {
                'high_roe_return': high_return,
                'low_roe_return': low_return,
                'return_spread': high_return - low_return,
                'factor_works': high_return > low_return
            }
        
        # 计算因子单调性
        returns = metrics_df['Annualized_Return'].values
        monotonicity = self.calculate_monotonicity(returns)
        analysis['monotonicity'] = monotonicity
        
        # IC分析（信息系数）
        ic_analysis = self.calculate_information_coefficient()
        analysis['ic_analysis'] = ic_analysis
        
        self.backtest_results['factor_analysis'] = analysis
        return analysis
    
    def calculate_monotonicity(self, returns: np.ndarray) -> float:
        """计算因子单调性"""
        if len(returns) < 2:
            return 0.0
        
        # 计算相邻组合收益率差异的符号一致性
        diffs = np.diff(returns)
        if len(diffs) == 0:
            return 0.0
        
        # 单调性 = 符号一致的比例
        signs = np.sign(diffs)
        if len(signs) == 0:
            return 0.0
        
        # 期望是递减的（高ROE组合收益更高）
        expected_sign = -1
        monotonicity = np.sum(signs == expected_sign) / len(signs)
        
        return monotonicity
    
    def calculate_information_coefficient(self) -> Dict:
        """计算信息系数"""
        try:
            if 'portfolio_returns' not in self.backtest_results:
                return {}
            
            portfolio_returns = self.backtest_results['portfolio_returns']
            portfolio_holdings = self.backtest_results['portfolio_holdings']
            
            ic_values = []
            
            # 对每个调仓期计算IC
            for group_name in portfolio_returns:
                returns_df = portfolio_returns[group_name]
                holdings = portfolio_holdings[group_name]
                
                for i, (_, row) in enumerate(returns_df.iterrows()):
                    if i < len(holdings):
                        period_return = row['return']
                        avg_roe = holdings[i]['avg_roe']
                        ic_values.append({'roe': avg_roe, 'return': period_return})
            
            if not ic_values:
                return {}
            
            ic_df = pd.DataFrame(ic_values)
            
            # 计算相关系数
            correlation = ic_df['roe'].corr(ic_df['return'])
            
            return {
                'ic_mean': correlation,
                'ic_std': 0,  # 简化处理
                'ic_ir': correlation / 0.1 if correlation != 0 else 0,  # 简化的IR
                'samples': len(ic_values)
            }
            
        except Exception as e:
            print(f"⚠️  计算IC失败: {e}")
            return {}
    
    def create_backtest_visualization(self, output_file: str = "roe_factor_backtest_results.html"):
        """创建回测结果可视化"""
        print(f"\n📊 创建回测结果可视化...")
        
        if 'portfolio_returns' not in self.backtest_results:
            print("❌ 请先运行回测模拟")
            return None
        
        portfolio_returns = self.backtest_results['portfolio_returns']
        
        # 创建子图
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('累积收益曲线', '年化收益率对比', '风险收益散点图', '回撤分析'),
            specs=[[{"secondary_y": False}, {"type": "bar"}],
                   [{"type": "scatter"}, {"type": "scatter"}]]
        )
        
        colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']
        
        # 1. 累积收益曲线
        for i, (group_name, returns_df) in enumerate(portfolio_returns.items()):
            if returns_df.empty:
                continue
            
            cumulative_returns = np.cumprod(1 + returns_df['return'].values)
            
            fig.add_trace(
                go.Scatter(
                    x=returns_df['date'],
                    y=cumulative_returns,
                    mode='lines',
                    name=group_name,
                    line=dict(color=colors[i % len(colors)]),
                    showlegend=True
                ),
                row=1, col=1
            )
        
        # 2. 年化收益率对比
        if 'performance_metrics' in self.backtest_results:
            metrics_df = self.backtest_results['performance_metrics']
            
            fig.add_trace(
                go.Bar(
                    x=metrics_df['Portfolio'],
                    y=metrics_df['Annualized_Return'] * 100,
                    name='年化收益率(%)',
                    marker_color=colors[:len(metrics_df)],
                    showlegend=False
                ),
                row=1, col=2
            )
            
            # 3. 风险收益散点图
            fig.add_trace(
                go.Scatter(
                    x=metrics_df['Volatility'] * 100,
                    y=metrics_df['Annualized_Return'] * 100,
                    mode='markers+text',
                    text=metrics_df['Portfolio'],
                    textposition='top center',
                    marker=dict(size=10, color=colors[:len(metrics_df)]),
                    name='风险收益',
                    showlegend=False
                ),
                row=2, col=1
            )
            
            # 4. 最大回撤
            fig.add_trace(
                go.Scatter(
                    x=metrics_df['Portfolio'],
                    y=metrics_df['Max_Drawdown'] * 100,
                    mode='markers+lines',
                    name='最大回撤(%)',
                    marker=dict(size=8, color='red'),
                    showlegend=False
                ),
                row=2, col=2
            )
        
        # 更新布局
        fig.update_layout(
            title_text="HSI ROE Factor Backtest Results",
            showlegend=True,
            height=800,
            width=1200
        )
        
        # 更新坐标轴标签
        fig.update_xaxes(title_text="日期", row=1, col=1)
        fig.update_yaxes(title_text="累积收益", row=1, col=1)
        
        fig.update_xaxes(title_text="投资组合", row=1, col=2)
        fig.update_yaxes(title_text="年化收益率(%)", row=1, col=2)
        
        fig.update_xaxes(title_text="波动率(%)", row=2, col=1)
        fig.update_yaxes(title_text="年化收益率(%)", row=2, col=1)
        
        fig.update_xaxes(title_text="投资组合", row=2, col=2)
        fig.update_yaxes(title_text="最大回撤(%)", row=2, col=2)
        
        # 保存为HTML文件
        pyo.plot(fig, filename=output_file, auto_open=False)
        print(f"✅ 可视化结果已保存: {output_file}")
        
        return output_file

    def generate_backtest_report(self, output_file: str = None) -> str:
        """生成回测报告"""
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"roe_factor_backtest_report_{timestamp}.md"

        print(f"\n📋 生成回测报告...")

        report_lines = []
        report_lines.append("# 恒生指数ROE因子回测报告")
        report_lines.append(f"## 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")

        # 回测设置
        report_lines.append("## 📊 回测设置")
        report_lines.append(f"- 调仓频率: 月度")
        report_lines.append(f"- 分组数量: 5组")
        report_lines.append(f"- 权重方式: 等权重")
        report_lines.append("")

        # 绩效指标
        if 'performance_metrics' in self.backtest_results:
            metrics_df = self.backtest_results['performance_metrics']

            report_lines.append("## 📈 绩效指标")
            report_lines.append("| 投资组合 | 年化收益率(%) | 波动率(%) | 夏普比率 | 最大回撤(%) | 胜率(%) |")
            report_lines.append("|---------|-------------|----------|---------|-----------|--------|")

            for _, row in metrics_df.iterrows():
                report_lines.append(
                    f"| {row['Portfolio']} | {row['Annualized_Return']*100:.2f} | "
                    f"{row['Volatility']*100:.2f} | {row['Sharpe_Ratio']:.2f} | "
                    f"{row['Max_Drawdown']*100:.2f} | {row['Win_Rate']*100:.2f} |"
                )

            report_lines.append("")

        # 因子有效性分析
        if 'factor_analysis' in self.backtest_results:
            analysis = self.backtest_results['factor_analysis']

            report_lines.append("## 🔍 因子有效性分析")

            if 'factor_effectiveness' in analysis:
                eff = analysis['factor_effectiveness']
                report_lines.append(f"### ROE因子效果")
                report_lines.append(f"- 高ROE组合年化收益: {eff['high_roe_return']*100:.2f}%")
                report_lines.append(f"- 低ROE组合年化收益: {eff['low_roe_return']*100:.2f}%")
                report_lines.append(f"- 收益差: {eff['return_spread']*100:.2f}%")
                report_lines.append(f"- 因子有效性: {'✅ 有效' if eff['factor_works'] else '❌ 无效'}")
                report_lines.append("")

            if 'monotonicity' in analysis:
                mono = analysis['monotonicity']
                report_lines.append(f"### 单调性分析")
                report_lines.append(f"- 单调性指标: {mono:.2f}")
                report_lines.append(f"- 单调性评价: {'✅ 良好' if mono > 0.6 else '⚠️ 一般' if mono > 0.4 else '❌ 较差'}")
                report_lines.append("")

            if 'ic_analysis' in analysis and analysis['ic_analysis']:
                ic = analysis['ic_analysis']
                report_lines.append(f"### 信息系数(IC)分析")
                report_lines.append(f"- IC均值: {ic['ic_mean']:.3f}")
                report_lines.append(f"- IC信息比率: {ic['ic_ir']:.3f}")
                report_lines.append(f"- 样本数量: {ic['samples']}")
                report_lines.append("")

        # 投资建议
        report_lines.append("## 💡 投资建议")

        if 'performance_metrics' in self.backtest_results:
            metrics_df = self.backtest_results['performance_metrics']

            # 找出最佳组合
            best_sharpe = metrics_df.loc[metrics_df['Sharpe_Ratio'].idxmax()]
            best_return = metrics_df.loc[metrics_df['Annualized_Return'].idxmax()]

            report_lines.append(f"### 推荐策略")
            report_lines.append(f"- 最佳夏普比率组合: {best_sharpe['Portfolio']} (夏普比率: {best_sharpe['Sharpe_Ratio']:.2f})")
            report_lines.append(f"- 最高收益组合: {best_return['Portfolio']} (年化收益: {best_return['Annualized_Return']*100:.2f}%)")
            report_lines.append("")

            # 风险提示
            report_lines.append(f"### 风险提示")
            max_dd = metrics_df['Max_Drawdown'].min()
            report_lines.append(f"- 最大回撤风险: {max_dd*100:.2f}%")

            if max_dd < -0.2:
                report_lines.append("- ⚠️ 回撤风险较高，建议控制仓位")
            elif max_dd < -0.1:
                report_lines.append("- ⚠️ 回撤风险中等，建议适度配置")
            else:
                report_lines.append("- ✅ 回撤风险可控")

        # 保存报告
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))

        print(f"✅ 回测报告已生成: {output_file}")
        return output_file

    def export_detailed_results(self, output_file: str = None) -> str:
        """导出详细回测结果"""
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"roe_factor_backtest_details_{timestamp}.xlsx"

        print(f"\n💾 导出详细回测结果...")

        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # 绩效指标
            if 'performance_metrics' in self.backtest_results:
                self.backtest_results['performance_metrics'].to_excel(
                    writer, sheet_name='Performance_Metrics', index=False
                )

            # 投资组合收益
            if 'portfolio_returns' in self.backtest_results:
                for group_name, returns_df in self.backtest_results['portfolio_returns'].items():
                    if not returns_df.empty:
                        sheet_name = group_name.replace('_', '')[:31]  # Excel工作表名称限制
                        returns_df.to_excel(writer, sheet_name=sheet_name, index=False)

            # 因子分析结果
            if 'factor_analysis' in self.backtest_results:
                analysis = self.backtest_results['factor_analysis']

                # 创建因子分析摘要
                summary_data = []

                if 'factor_effectiveness' in analysis:
                    eff = analysis['factor_effectiveness']
                    summary_data.append({
                        'Metric': 'High ROE Return',
                        'Value': eff['high_roe_return']
                    })
                    summary_data.append({
                        'Metric': 'Low ROE Return',
                        'Value': eff['low_roe_return']
                    })
                    summary_data.append({
                        'Metric': 'Return Spread',
                        'Value': eff['return_spread']
                    })

                if 'monotonicity' in analysis:
                    summary_data.append({
                        'Metric': 'Monotonicity',
                        'Value': analysis['monotonicity']
                    })

                if 'ic_analysis' in analysis and analysis['ic_analysis']:
                    ic = analysis['ic_analysis']
                    summary_data.append({
                        'Metric': 'IC Mean',
                        'Value': ic['ic_mean']
                    })
                    summary_data.append({
                        'Metric': 'IC IR',
                        'Value': ic['ic_ir']
                    })

                if summary_data:
                    summary_df = pd.DataFrame(summary_data)
                    summary_df.to_excel(writer, sheet_name='Factor_Analysis', index=False)

        print(f"✅ 详细结果已导出: {output_file}")
        return output_file


def main():
    """主函数"""
    print("🚀 恒生指数ROE因子回测工具")
    print("=" * 60)

    # 创建回测器
    backtester = HSIROEFactorBacktest()

    # 加载数据
    if not backtester.load_data():
        return

    # 设置回测参数
    print("\n⚙️ 回测参数设置:")
    print("1. 默认设置 (2015-2024, 月度调仓, 5分组)")
    print("2. 自定义设置")

    choice = input("请选择 (1-2): ").strip()

    if choice == "2":
        start_date = input("请输入开始日期 (YYYY-MM-DD, 默认2015-01-01): ").strip()
        end_date = input("请输入结束日期 (YYYY-MM-DD, 默认2024-12-31): ").strip()
        n_groups = input("请输入分组数量 (默认5): ").strip()

        start_date = start_date if start_date else "2015-01-01"
        end_date = end_date if end_date else "2024-12-31"
        n_groups = int(n_groups) if n_groups.isdigit() else 5
    else:
        start_date = "2015-01-01"
        end_date = "2024-12-31"
        n_groups = 5

    print(f"\n📅 回测设置:")
    print(f"   - 开始日期: {start_date}")
    print(f"   - 结束日期: {end_date}")
    print(f"   - 分组数量: {n_groups}")

    # 准备回测数据
    backtest_data, rebalance_dates = backtester.prepare_backtest_data(start_date, end_date)

    # 运行回测
    portfolio_returns = backtester.simulate_portfolio_returns(backtest_data, rebalance_dates, n_groups)

    # 计算绩效指标
    metrics_df = backtester.calculate_performance_metrics()

    if not metrics_df.empty:
        print(f"\n📊 回测结果摘要:")
        print(metrics_df[['Portfolio', 'Annualized_Return', 'Volatility', 'Sharpe_Ratio', 'Max_Drawdown']].to_string(index=False))

    # 分析因子有效性
    factor_analysis = backtester.analyze_roe_factor_effectiveness()

    if factor_analysis:
        print(f"\n🔍 ROE因子有效性分析:")
        if 'factor_effectiveness' in factor_analysis:
            eff = factor_analysis['factor_effectiveness']
            print(f"   - 高ROE vs 低ROE收益差: {eff['return_spread']*100:.2f}%")
            print(f"   - 因子有效性: {'✅ 有效' if eff['factor_works'] else '❌ 无效'}")

        if 'monotonicity' in factor_analysis:
            mono = factor_analysis['monotonicity']
            print(f"   - 单调性: {mono:.2f}")

    # 生成可视化
    viz_file = backtester.create_backtest_visualization()

    # 生成报告
    report_file = backtester.generate_backtest_report()

    # 导出详细结果
    excel_file = backtester.export_detailed_results()

    print(f"\n🎉 ROE因子回测完成！")
    print(f"📊 可视化结果: {viz_file}")
    print(f"📋 回测报告: {report_file}")
    print(f"💾 详细数据: {excel_file}")


if __name__ == "__main__":
    main()
