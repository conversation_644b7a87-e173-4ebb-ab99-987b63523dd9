#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
恒生指数成分股每日ROE数据获取工具

功能：
1. 获取恒生指数成分股列表
2. 获取每只股票的财务数据（包括ROE）
3. 将季度ROE数据扩展到每日数据
4. 缓存数据以提高效率
5. 支持增量更新

作者: AI Assistant
创建时间: 2025年1月
"""

import os
import sys
import pandas as pd
import numpy as np
import requests
import time
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import akshare as ak

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('hsi_daily_roe_fetcher.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class HSIROEFetcher:
    """恒生指数成分股ROE数据获取器"""
    
    def __init__(self, cache_dir: str = "hsi_roe_cache"):
        self.cache_dir = cache_dir
        self.constituents_file = "data_files/hsi_constituents.csv"
        self.hsi_constituents = []
        
        # 创建缓存目录
        os.makedirs(cache_dir, exist_ok=True)
        os.makedirs(f"{cache_dir}/quarterly_data", exist_ok=True)
        os.makedirs(f"{cache_dir}/daily_data", exist_ok=True)
        
        # 请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Referer': 'https://emweb.securities.eastmoney.com/'
        }
    
    def load_hsi_constituents(self) -> bool:
        """加载恒生指数成分股列表"""
        try:
            logger.info(f"📁 从 {self.constituents_file} 加载恒生指数成分股...")
            
            df = pd.read_csv(self.constituents_file, dtype={'代码': str})
            
            # 格式化股票代码
            hsi_stocks = []
            for _, row in df.iterrows():
                code = str(row['代码']).strip()
                name = str(row['名称']).strip()
                if code and code != 'nan':
                    code_formatted = str(code).zfill(5)  # 补齐到5位数
                    hsi_stocks.append({
                        'code': code_formatted,
                        'name': name
                    })
            
            self.hsi_constituents = hsi_stocks
            logger.info(f"✅ 已加载 {len(hsi_stocks)} 只恒生指数成分股")
            return True
            
        except Exception as e:
            logger.error(f"❌ 加载成分股列表失败: {e}")
            return False
    
    def format_hk_stock_code(self, stock_code: str) -> str:
        """格式化港股代码为东方财富网格式"""
        # 确保是5位数字
        formatted_code = str(stock_code).zfill(5)
        # 添加.HK后缀
        return f"{formatted_code}.HK"
    
    def get_quarterly_roe_data(self, stock_code: str, stock_name: str) -> Optional[pd.DataFrame]:
        """获取单只股票的季度ROE数据"""
        try:
            # 格式化股票代码
            formatted_code = self.format_hk_stock_code(stock_code)
            logger.info(f"📊 获取 {formatted_code} ({stock_name}) 的ROE数据...")
            
            # 检查缓存
            cache_file = f"{self.cache_dir}/quarterly_data/{stock_code}_quarterly_roe.csv"
            if os.path.exists(cache_file):
                # 检查缓存是否过期（超过1天）
                cache_time = os.path.getmtime(cache_file)
                if time.time() - cache_time < 86400:  # 24小时
                    logger.info(f"📁 使用缓存数据: {stock_code}")
                    return pd.read_csv(cache_file, parse_dates=['report_date'])
            
            # 使用东方财富网API获取财务指标数据
            url = "https://datacenter.eastmoney.com/securities/api/data/v1/get"
            params = {
                'reportName': 'RPT_HKF10_FN_MAININDICATOR',
                'columns': 'ALL',
                'quoteColumns': '',
                'filter': f'(SECUCODE="{formatted_code}")',
                'pageNumber': '1',
                'pageSize': '50',
                'sortTypes': '-1',
                'sortColumns': 'STD_REPORT_DATE',
                'source': 'F10',
                'client': 'PC'
            }
            
            response = requests.get(url, params=params, headers=self.headers, timeout=30)
            
            if response.status_code != 200:
                logger.warning(f"⚠️  请求失败: {stock_code}, 状态码: {response.status_code}")
                return None
            
            data = response.json()
            
            if 'result' not in data or 'data' not in data['result'] or not data['result']['data']:
                logger.warning(f"⚠️  无财务数据: {stock_code}")
                return None
            
            # 解析数据
            financial_data = data['result']['data']
            roe_records = []
            
            for record in financial_data:
                try:
                    # 提取ROE相关字段
                    report_date = record.get('STD_REPORT_DATE')
                    if not report_date:
                        continue
                    
                    # 尝试多个ROE字段
                    roe_value = None
                    roe_fields = ['ROE_AVG', 'WEIGHTAVG_ROE', 'ROE', 'ROEAVG', 'AVGROE']
                    
                    for field in roe_fields:
                        if field in record and record[field] is not None:
                            try:
                                roe_value = float(record[field])
                                break
                            except (ValueError, TypeError):
                                continue
                    
                    if roe_value is not None:
                        roe_records.append({
                            'stock_code': stock_code,
                            'stock_name': stock_name,
                            'report_date': pd.to_datetime(report_date),
                            'roe': roe_value
                        })
                
                except Exception as e:
                    logger.warning(f"⚠️  解析记录失败: {e}")
                    continue
            
            if not roe_records:
                logger.warning(f"⚠️  未找到ROE数据: {stock_code}")
                return None
            
            # 创建DataFrame
            df = pd.DataFrame(roe_records)
            df = df.sort_values('report_date').reset_index(drop=True)
            
            # 保存到缓存
            df.to_csv(cache_file, index=False)
            logger.info(f"✅ 获取到 {stock_code} 的 {len(df)} 条ROE记录")
            
            return df
            
        except Exception as e:
            logger.error(f"❌ 获取 {stock_code} ROE数据失败: {e}")
            return None
    
    def expand_to_daily_roe(self, quarterly_df: pd.DataFrame, start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """将季度ROE数据扩展为每日数据"""
        try:
            if quarterly_df is None or quarterly_df.empty:
                return pd.DataFrame()
            
            # 设置日期范围
            if start_date is None:
                start_date = quarterly_df['report_date'].min()
            else:
                start_date = pd.to_datetime(start_date)
            
            if end_date is None:
                end_date = datetime.now()
            else:
                end_date = pd.to_datetime(end_date)
            
            # 创建每日日期序列
            date_range = pd.date_range(start=start_date, end=end_date, freq='D')
            
            # 创建每日数据框架
            daily_data = []
            
            stock_code = quarterly_df['stock_code'].iloc[0]
            stock_name = quarterly_df['stock_name'].iloc[0]
            
            # 对每个日期，找到最近的已公布ROE数据
            for date in date_range:
                # 找到该日期之前最近的财报数据
                available_data = quarterly_df[quarterly_df['report_date'] <= date]
                
                if not available_data.empty:
                    # 使用最近的ROE数据
                    latest_roe = available_data.iloc[-1]['roe']
                    
                    daily_data.append({
                        'date': date,
                        'stock_code': stock_code,
                        'stock_name': stock_name,
                        'roe': latest_roe
                    })
            
            if not daily_data:
                return pd.DataFrame()
            
            daily_df = pd.DataFrame(daily_data)
            return daily_df
            
        except Exception as e:
            logger.error(f"❌ 扩展每日ROE数据失败: {e}")
            return pd.DataFrame()

    def get_daily_roe_data(self, stock_code: str, stock_name: str, start_date: str = None, end_date: str = None) -> Optional[pd.DataFrame]:
        """获取单只股票的每日ROE数据"""
        try:
            # 检查每日数据缓存
            cache_file = f"{self.cache_dir}/daily_data/{stock_code}_daily_roe.csv"

            # 如果有缓存且不需要更新，直接返回
            if os.path.exists(cache_file) and start_date is None and end_date is None:
                cache_time = os.path.getmtime(cache_file)
                if time.time() - cache_time < 86400:  # 24小时内的缓存
                    logger.info(f"📁 使用每日ROE缓存: {stock_code}")
                    df = pd.read_csv(cache_file, parse_dates=['date'])
                    return df

            # 获取季度数据
            quarterly_df = self.get_quarterly_roe_data(stock_code, stock_name)
            if quarterly_df is None or quarterly_df.empty:
                return None

            # 扩展为每日数据
            daily_df = self.expand_to_daily_roe(quarterly_df, start_date, end_date)
            if daily_df.empty:
                return None

            # 保存到缓存
            daily_df.to_csv(cache_file, index=False)
            logger.info(f"✅ 生成 {stock_code} 每日ROE数据: {len(daily_df)} 天")

            return daily_df

        except Exception as e:
            logger.error(f"❌ 获取 {stock_code} 每日ROE数据失败: {e}")
            return None

    def get_all_stocks_daily_roe(self, start_date: str = None, end_date: str = None, max_stocks: int = None) -> Dict[str, pd.DataFrame]:
        """获取所有恒生指数成分股的每日ROE数据"""
        try:
            if not self.hsi_constituents:
                logger.error("❌ 请先加载恒生指数成分股列表")
                return {}

            logger.info(f"🚀 开始获取所有恒生指数成分股的每日ROE数据...")
            logger.info(f"📅 日期范围: {start_date or '最早'} 到 {end_date or '最新'}")

            all_data = {}
            success_count = 0
            total_stocks = len(self.hsi_constituents)

            # 限制处理的股票数量（用于测试）
            if max_stocks:
                stocks_to_process = self.hsi_constituents[:max_stocks]
                logger.info(f"🔬 测试模式：只处理前 {max_stocks} 只股票")
            else:
                stocks_to_process = self.hsi_constituents

            for i, stock in enumerate(stocks_to_process, 1):
                stock_code = stock['code']
                stock_name = stock['name']

                logger.info(f"📊 [{i}/{len(stocks_to_process)}] 处理 {stock_code} ({stock_name})")

                try:
                    daily_roe_df = self.get_daily_roe_data(stock_code, stock_name, start_date, end_date)

                    if daily_roe_df is not None and not daily_roe_df.empty:
                        all_data[stock_code] = daily_roe_df
                        success_count += 1
                        logger.info(f"✅ 成功获取 {stock_code} 数据")
                    else:
                        logger.warning(f"⚠️  {stock_code} 无ROE数据")

                    # 添加延迟避免请求过于频繁
                    time.sleep(1)

                except Exception as e:
                    logger.error(f"❌ 处理 {stock_code} 失败: {e}")
                    continue

            logger.info(f"🎉 完成！成功获取 {success_count}/{len(stocks_to_process)} 只股票的ROE数据")
            return all_data

        except Exception as e:
            logger.error(f"❌ 批量获取ROE数据失败: {e}")
            return {}

    def save_combined_data(self, all_data: Dict[str, pd.DataFrame], output_file: str = None) -> str:
        """保存合并的ROE数据"""
        try:
            if not all_data:
                logger.warning("⚠️  没有数据需要保存")
                return None

            if output_file is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_file = f"{self.cache_dir}/hsi_daily_roe_combined_{timestamp}.csv"

            # 合并所有数据
            combined_data = []
            for stock_code, df in all_data.items():
                combined_data.append(df)

            if combined_data:
                combined_df = pd.concat(combined_data, ignore_index=True)
                combined_df = combined_df.sort_values(['date', 'stock_code']).reset_index(drop=True)

                # 保存数据
                combined_df.to_csv(output_file, index=False, encoding='utf-8-sig')
                logger.info(f"💾 已保存合并数据到: {output_file}")
                logger.info(f"📊 数据统计: {len(combined_df)} 条记录，{len(all_data)} 只股票")

                return output_file
            else:
                logger.warning("⚠️  没有有效数据可合并")
                return None

        except Exception as e:
            logger.error(f"❌ 保存合并数据失败: {e}")
            return None

    def generate_summary_report(self, all_data: Dict[str, pd.DataFrame]) -> str:
        """生成ROE数据摘要报告"""
        try:
            if not all_data:
                return "没有数据可分析"

            report_lines = []
            report_lines.append("# 恒生指数成分股每日ROE数据获取报告")
            report_lines.append(f"## 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            report_lines.append("")

            # 基本统计
            total_stocks = len(all_data)
            total_records = sum(len(df) for df in all_data.values())

            report_lines.append("## 📊 基本统计")
            report_lines.append(f"- 成功获取股票数: {total_stocks}")
            report_lines.append(f"- 总记录数: {total_records:,}")
            report_lines.append("")

            # 数据质量统计
            report_lines.append("## 📈 数据质量统计")

            stock_stats = []
            for stock_code, df in all_data.items():
                if not df.empty:
                    stock_name = df['stock_name'].iloc[0]
                    date_range = f"{df['date'].min().date()} 到 {df['date'].max().date()}"
                    avg_roe = df['roe'].mean()
                    latest_roe = df['roe'].iloc[-1]

                    stock_stats.append({
                        'stock_code': stock_code,
                        'stock_name': stock_name,
                        'records': len(df),
                        'date_range': date_range,
                        'avg_roe': avg_roe,
                        'latest_roe': latest_roe
                    })

            # 按最新ROE排序
            stock_stats.sort(key=lambda x: x['latest_roe'], reverse=True)

            report_lines.append("### 前10只高ROE股票（按最新ROE排序）")
            report_lines.append("| 股票代码 | 股票名称 | 记录数 | 最新ROE(%) | 平均ROE(%) |")
            report_lines.append("|---------|---------|--------|-----------|-----------|")

            for stock in stock_stats[:10]:
                report_lines.append(f"| {stock['stock_code']} | {stock['stock_name']} | {stock['records']} | {stock['latest_roe']:.2f} | {stock['avg_roe']:.2f} |")

            report_lines.append("")

            # 保存报告
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = f"{self.cache_dir}/hsi_roe_summary_report_{timestamp}.md"

            with open(report_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(report_lines))

            logger.info(f"📋 已生成摘要报告: {report_file}")
            return report_file

        except Exception as e:
            logger.error(f"❌ 生成摘要报告失败: {e}")
            return None


def main():
    """主函数：演示如何使用HSIROEFetcher"""
    print("🚀 恒生指数成分股每日ROE数据获取工具")
    print("=" * 60)

    # 创建ROE数据获取器
    fetcher = HSIROEFetcher()

    # 加载恒生指数成分股
    if not fetcher.load_hsi_constituents():
        print("❌ 加载成分股失败，程序退出")
        return

    print(f"✅ 已加载 {len(fetcher.hsi_constituents)} 只恒生指数成分股")
    print()

    # 选择运行模式
    print("请选择运行模式:")
    print("1. 测试模式 - 获取前5只股票的ROE数据")
    print("2. 单只股票模式 - 获取指定股票的ROE数据")
    print("3. 完整模式 - 获取所有股票的ROE数据")
    print("4. 指定日期范围模式 - 获取指定时间段的ROE数据")

    try:
        choice = input("请输入选择 (1-4): ").strip()

        if choice == "1":
            # 测试模式
            print("\n🔬 测试模式：获取前5只股票的ROE数据")
            all_data = fetcher.get_all_stocks_daily_roe(max_stocks=5)

        elif choice == "2":
            # 单只股票模式
            print("\n📊 单只股票模式")
            stock_code = input("请输入股票代码 (如: 00700): ").strip()

            # 查找股票名称
            stock_name = "未知"
            for stock in fetcher.hsi_constituents:
                if stock['code'] == stock_code:
                    stock_name = stock['name']
                    break

            print(f"获取 {stock_code} ({stock_name}) 的ROE数据...")
            daily_roe = fetcher.get_daily_roe_data(stock_code, stock_name)

            if daily_roe is not None:
                all_data = {stock_code: daily_roe}
                print(f"✅ 成功获取 {len(daily_roe)} 天的ROE数据")
            else:
                all_data = {}
                print("❌ 获取失败")

        elif choice == "3":
            # 完整模式
            print("\n🌟 完整模式：获取所有股票的ROE数据")
            confirm = input("这将需要较长时间，确认继续？(y/N): ").strip().lower()
            if confirm == 'y':
                all_data = fetcher.get_all_stocks_daily_roe()
            else:
                print("已取消")
                return

        elif choice == "4":
            # 指定日期范围模式
            print("\n📅 指定日期范围模式")
            start_date = input("请输入开始日期 (YYYY-MM-DD，留空为最早): ").strip()
            end_date = input("请输入结束日期 (YYYY-MM-DD，留空为最新): ").strip()

            start_date = start_date if start_date else None
            end_date = end_date if end_date else None

            max_stocks = input("限制股票数量 (留空为全部): ").strip()
            max_stocks = int(max_stocks) if max_stocks.isdigit() else None

            all_data = fetcher.get_all_stocks_daily_roe(start_date, end_date, max_stocks)

        else:
            print("❌ 无效选择")
            return

        # 处理结果
        if all_data:
            print(f"\n🎉 成功获取 {len(all_data)} 只股票的ROE数据")

            # 保存合并数据
            output_file = fetcher.save_combined_data(all_data)
            if output_file:
                print(f"💾 数据已保存到: {output_file}")

            # 生成摘要报告
            report_file = fetcher.generate_summary_report(all_data)
            if report_file:
                print(f"📋 摘要报告已生成: {report_file}")

            # 显示简单统计
            print("\n📊 数据统计:")
            for stock_code, df in list(all_data.items())[:5]:  # 只显示前5只
                stock_name = df['stock_name'].iloc[0]
                latest_roe = df['roe'].iloc[-1]
                print(f"  {stock_code} ({stock_name}): 最新ROE = {latest_roe:.2f}%")

            if len(all_data) > 5:
                print(f"  ... 还有 {len(all_data) - 5} 只股票")

        else:
            print("❌ 未获取到任何数据")

    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断程序")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")
        logger.error(f"主程序执行出错: {e}")


if __name__ == "__main__":
    main()
